# Pilot Experiment: QA + Summarization on ArXiv Papers

This pilot experiment combines **Question-Answer generation** and **Text Summarization** tasks using the `ccdv/arxiv-summarization` dataset with visual noise injection.

## 🎯 Experiment Overview

The pilot experiment processes 100 ArXiv papers and performs:

1. **QA Generation**: Generates 5 question-answer pairs per article
2. **Text Summarization**: Creates clean and noisy document images, then summarizes using VLM
3. **Evaluation**: Measures performance on both tasks with comprehensive metrics
4. **Noise Impact Analysis**: Compares clean vs noisy performance

## 📁 File Structure

```
├── pilot_experiment.py      # Main experiment class
├── run_pilot.py            # Simple script to run experiment
├── pilot_config.py         # Configuration settings
├── requirements_pilot.txt  # Python dependencies
├── PILOT_README.md         # This file
└── pilot_results/          # Output directory (created automatically)
    ├── images/             # Generated document images
    ├── qa_data/            # QA pairs data
    ├── pilot_results_*.csv # Main results
    ├── detailed_results_*.json # Detailed JSON results
    └── experiment_report.txt # Summary report
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements_pilot.txt
```

### 2. Run the Pilot Experiment

**Basic usage (100 samples):**
```bash
python run_pilot.py
```

**Custom parameters:**
```bash
python run_pilot.py --samples 50 --output-dir my_results --qa-pairs 3
```

**All options:**
```bash
python run_pilot.py \
    --samples 100 \
    --output-dir pilot_results \
    --min-noise 3 \
    --max-noise 8 \
    --qa-pairs 5
```

### 3. Monitor Progress

The experiment will show progress bars and status updates:
```
Processing sample_001...
  - Generating QA pairs...
  - Running summarization pipeline...
  - Evaluating QA pairs...
  - Evaluating summaries...
  - Completed sample_001
```

## 📊 Output Files

### Main Results (`pilot_results_TIMESTAMP.csv`)
Contains per-sample metrics:
- `sample_id`: Unique identifier
- `summary_clean/noisy`: Generated summaries
- `qa_accuracy_clean/noisy`: QA accuracy scores
- `rougeL_clean/noise`: ROUGE-L scores
- `bert_F_clean/noise`: BERT-F1 scores
- Article/abstract statistics

### QA Data (`qa_data_TIMESTAMP.csv`)
Contains all generated QA pairs:
- `sample_id`: Source sample
- `question_id`: Unique question identifier
- `question`: Generated question
- `answer`: Generated answer

### Summary Report (`experiment_report.txt`)
Contains aggregate statistics:
- Mean ± std for all metrics
- Noise impact analysis
- Data statistics

## 🔧 Configuration

Modify `pilot_config.py` to customize:
- Model names and parameters
- Noise injection settings
- Output preferences
- Evaluation metrics

## 📈 Expected Results

The experiment will generate metrics for:

### Summarization Task
- **ROUGE-L**: Lexical overlap with gold abstracts
- **BERT-F1**: Semantic similarity scores
- **Noise Impact**: Performance degradation due to visual noise

### QA Task
- **Accuracy**: Exact match accuracy for generated QA pairs
- **Clean vs Noisy**: Performance comparison on clean vs noisy images

### Sample Expected Output
```
SUMMARIZATION METRICS:
ROUGE-L Clean:  0.2435 ± 0.1234
ROUGE-L Noisy:  0.1917 ± 0.1156
BERT-F1 Clean:  0.8430 ± 0.0892
BERT-F1 Noisy:  0.8344 ± 0.0934

QA METRICS:
QA Accuracy Clean: 0.6200 ± 0.2145
QA Accuracy Noisy: 0.5800 ± 0.2234

NOISE IMPACT:
Avg ROUGE-L Drop: 0.0518
Avg BERT-F1 Drop: 0.0086
Avg QA Acc Drop:  0.0400
```

## 🛠️ Troubleshooting

### Common Issues

1. **CUDA Memory Error**
   - Reduce batch size in model loading
   - Use smaller models if needed

2. **Image Generation Fails**
   - Install `wkhtmltopdf`: `sudo apt-get install wkhtmltopdf`
   - Check `imgkit` configuration

3. **Model Loading Issues**
   - Ensure sufficient disk space for model downloads
   - Check internet connection for HuggingFace downloads

4. **Slow Processing**
   - Expected: ~2-3 minutes per sample
   - Use fewer samples for testing: `--samples 10`

### Performance Tips

- **GPU Usage**: Ensure CUDA is available for faster processing
- **Parallel Processing**: Models automatically use available GPUs
- **Memory Management**: Close other applications to free RAM

## 🔍 Understanding the Results

### Good Performance Indicators
- ROUGE-L > 0.20 for summarization
- QA Accuracy > 0.50 for question answering
- Noise impact < 0.10 drop in performance

### Analysis Questions
1. How much does visual noise affect each task?
2. Which task is more robust to visual disturbances?
3. Are there patterns in which types of questions are affected most?

## 📝 Next Steps

After running the pilot:

1. **Analyze Results**: Review the generated report and CSV files
2. **Scale Up**: Increase sample size for full experiment
3. **Optimize**: Adjust noise parameters based on impact analysis
4. **Extend**: Add more evaluation metrics or tasks

## 🤝 Support

If you encounter issues:
1. Check the error messages in the console output
2. Review the configuration in `pilot_config.py`
3. Ensure all dependencies are installed correctly
4. Try with a smaller sample size first (`--samples 5`)

---

**Happy Experimenting! 🚀**
