# pilot_experiment.py
"""
Pilot Experiment: QA Generation + Text Summarization on ArXiv Papers
- Uses 100 samples from ccdv/arxiv-summarization dataset
- Generates QA pairs from article text
- Performs text summarization (both clean and with visual noise)
- Evaluates both tasks with comprehensive metrics
"""

import os
import json
import random
import pandas as pd
from datetime import datetime
from datasets import load_dataset
from tqdm import tqdm

# Import your existing modules
from core_selection import CoreSentenceSelector
from image_generation import ImageGenerator
from inference import VLMInference
from qa_generation import QAGenerator
from evaluate import evaluate_summaries, evaluate_qa_accuracy

class PilotExperiment:
    def __init__(self, num_samples=100, output_dir="pilot_results"):
        self.num_samples = num_samples
        self.output_dir = output_dir
        self.setup_directories()
        
        # Initialize modules
        print("Initializing models...")
        self.core_selector = CoreSentenceSelector()
        self.image_gen = ImageGenerator(img_dir=os.path.join(output_dir, 'images'))
        self.vlm_infer = VLMInference()
        self.qa_generator = QAGenerator()
        print("Models initialized successfully!")
        
        # Experiment parameters
        self.min_noise = 3
        self.max_noise = 8
        self.num_qa_pairs = 5  # Generate 5 QA pairs per article
        
    def setup_directories(self):
        """Create necessary directories for the experiment"""
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'images'), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'qa_data'), exist_ok=True)
        
    def load_data(self):
        """Load and sample data from arxiv-summarization dataset"""
        print(f"Loading {self.num_samples} samples from ccdv/arxiv-summarization...")
        dataset = load_dataset("ccdv/arxiv-summarization", split="train")
        
        # Sample random indices for reproducibility
        random.seed(42)
        sample_indices = random.sample(range(len(dataset)), self.num_samples)
        self.samples = [dataset[i] for i in sample_indices]
        
        print(f"Loaded {len(self.samples)} samples successfully!")
        return self.samples
    
    def generate_qa_pairs(self, article, sample_id):
        """Generate QA pairs from article text"""
        try:
            qa_pairs = self.qa_generator.generate_qa(article, num_qas=self.num_qa_pairs)
            
            # Parse QA pairs (assuming format: "1. Question || Answer")
            parsed_qa = []
            for i, qa_text in enumerate(qa_pairs):
                if "||" in qa_text:
                    question, answer = qa_text.split("||", 1)
                    question = question.strip().lstrip("0123456789. ")
                    answer = answer.strip()
                    parsed_qa.append({
                        "question_id": f"{sample_id}_q{i+1}",
                        "question": question,
                        "answer": answer
                    })
            
            return parsed_qa
        except Exception as e:
            print(f"Error generating QA for sample {sample_id}: {e}")
            return []
    
    def run_summarization_pipeline(self, article, sample_id):
        """Run the complete summarization pipeline (clean + noisy)"""
        try:
            # Extract core sentences
            all_sents, core_sents = self.core_selector.extract_core_sentences(article)
            
            # Select noise sentences
            noise_sents = self.image_gen.pick_noise_sentences(
                all_sents, core_sents, self.min_noise, self.max_noise
            )
            
            # Generate noisy HTML
            noisy_html, noise_record = self.image_gen.inject_visual_noise(article, noise_sents)
            
            # Generate images
            clean_img_path = os.path.join(self.output_dir, 'images', f'clean_{sample_id}.png')
            noisy_img_path = os.path.join(self.output_dir, 'images', f'noisy_{sample_id}.png')
            
            self.image_gen.html_to_png(article, clean_img_path)
            self.image_gen.html_to_png(noisy_html, noisy_img_path)
            
            # Generate summaries
            summary_clean = self.vlm_infer.summarize_from_image(clean_img_path)
            summary_noisy = self.vlm_infer.summarize_from_image(noisy_img_path)
            
            return {
                'core_sents': core_sents,
                'noise_sents': noise_sents,
                'noise_record': noise_record,
                'summary_clean': summary_clean,
                'summary_noisy': summary_noisy,
                'clean_img_path': clean_img_path,
                'noisy_img_path': noisy_img_path
            }
            
        except Exception as e:
            print(f"Error in summarization pipeline for sample {sample_id}: {e}")
            return None
    
    def evaluate_qa_with_vlm(self, qa_pairs, clean_img_path, noisy_img_path):
        """Evaluate QA pairs using VLM on both clean and noisy images"""
        clean_results = []
        noisy_results = []
        
        for qa in qa_pairs:
            try:
                # Get VLM answers for clean image
                clean_answer = self.vlm_infer.answer_question_from_image(
                    clean_img_path, qa['question']
                )
                clean_results.append({
                    'question_id': qa['question_id'],
                    'predicted_answer': clean_answer,
                    'ground_truth': qa['answer']
                })
                
                # Get VLM answers for noisy image
                noisy_answer = self.vlm_infer.answer_question_from_image(
                    noisy_img_path, qa['question']
                )
                noisy_results.append({
                    'question_id': qa['question_id'],
                    'predicted_answer': noisy_answer,
                    'ground_truth': qa['answer']
                })
                
            except Exception as e:
                print(f"Error evaluating QA {qa['question_id']}: {e}")
                
        return clean_results, noisy_results
    
    def run_experiment(self):
        """Run the complete pilot experiment"""
        print("="*60)
        print("STARTING PILOT EXPERIMENT")
        print(f"Samples: {self.num_samples}")
        print(f"Output Directory: {self.output_dir}")
        print("="*60)
        
        # Load data
        samples = self.load_data()
        
        # Results storage
        results = []
        all_qa_data = []
        
        # Process each sample
        for idx, sample in enumerate(tqdm(samples, desc="Processing samples")):
            sample_id = f"sample_{idx:03d}"
            article = sample['article']
            abstract_gold = sample['abstract']
            
            print(f"\nProcessing {sample_id}...")
            
            # Generate QA pairs
            print("  - Generating QA pairs...")
            qa_pairs = self.generate_qa_pairs(article, sample_id)
            
            # Run summarization pipeline
            print("  - Running summarization pipeline...")
            summ_results = self.run_summarization_pipeline(article, sample_id)
            
            if summ_results is None:
                print(f"  - Skipping {sample_id} due to summarization error")
                continue
            
            # Evaluate QA with VLM
            print("  - Evaluating QA pairs...")
            qa_clean_results, qa_noisy_results = self.evaluate_qa_with_vlm(
                qa_pairs, summ_results['clean_img_path'], summ_results['noisy_img_path']
            )
            
            # Evaluate summaries
            print("  - Evaluating summaries...")
            summ_metrics = evaluate_summaries(
                summ_results['summary_clean'], 
                summ_results['summary_noisy'], 
                abstract_gold
            )
            
            # Calculate QA accuracy
            qa_clean_acc = evaluate_qa_accuracy(
                [r['predicted_answer'] for r in qa_clean_results],
                [r['ground_truth'] for r in qa_clean_results]
            )
            qa_noisy_acc = evaluate_qa_accuracy(
                [r['predicted_answer'] for r in qa_noisy_results],
                [r['ground_truth'] for r in qa_noisy_results]
            )
            
            # Store results
            result = {
                'sample_id': sample_id,
                'article_length': len(article),
                'abstract_length': len(abstract_gold),
                'num_qa_pairs': len(qa_pairs),
                'num_core_sents': len(summ_results['core_sents']),
                'num_noise_sents': len(summ_results['noise_sents']),
                'summary_clean': summ_results['summary_clean'],
                'summary_noisy': summ_results['summary_noisy'],
                'abstract_gold': abstract_gold,
                'qa_accuracy_clean': qa_clean_acc,
                'qa_accuracy_noisy': qa_noisy_acc,
                **summ_metrics
            }
            results.append(result)
            
            # Store QA data separately
            for qa in qa_pairs:
                qa['sample_id'] = sample_id
            all_qa_data.extend(qa_pairs)
            
            print(f"  - Completed {sample_id}")
        
        # Save results
        self.save_results(results, all_qa_data)
        
        # Generate summary report
        self.generate_report(results)
        
        print("\n" + "="*60)
        print("PILOT EXPERIMENT COMPLETED!")
        print(f"Results saved to: {self.output_dir}")
        print("="*60)
        
        return results
    
    def save_results(self, results, qa_data):
        """Save experiment results to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save main results
        results_df = pd.DataFrame(results)
        results_path = os.path.join(self.output_dir, f'pilot_results_{timestamp}.csv')
        results_df.to_csv(results_path, index=False)
        
        # Save QA data
        qa_df = pd.DataFrame(qa_data)
        qa_path = os.path.join(self.output_dir, f'qa_data_{timestamp}.csv')
        qa_df.to_csv(qa_path, index=False)
        
        # Save detailed results as JSON
        json_path = os.path.join(self.output_dir, f'detailed_results_{timestamp}.json')
        with open(json_path, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"Results saved:")
        print(f"  - Main results: {results_path}")
        print(f"  - QA data: {qa_path}")
        print(f"  - Detailed JSON: {json_path}")
    
    def generate_report(self, results):
        """Generate a summary report of the experiment"""
        if not results:
            print("No results to generate report from.")
            return
        
        df = pd.DataFrame(results)
        
        report = f"""
PILOT EXPERIMENT SUMMARY REPORT
===============================
Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
Total Samples Processed: {len(results)}

SUMMARIZATION METRICS:
---------------------
ROUGE-L Clean:  {df['rougeL_clean'].mean():.4f} ± {df['rougeL_clean'].std():.4f}
ROUGE-L Noisy:  {df['rougeL_noise'].mean():.4f} ± {df['rougeL_noise'].std():.4f}
BERT-F1 Clean:  {df['bert_F_clean'].mean():.4f} ± {df['bert_F_clean'].std():.4f}
BERT-F1 Noisy:  {df['bert_F_noise'].mean():.4f} ± {df['bert_F_noise'].std():.4f}

QA METRICS:
----------
QA Accuracy Clean: {df['qa_accuracy_clean'].mean():.4f} ± {df['qa_accuracy_clean'].std():.4f}
QA Accuracy Noisy: {df['qa_accuracy_noisy'].mean():.4f} ± {df['qa_accuracy_noisy'].std():.4f}

NOISE IMPACT:
------------
Avg ROUGE-L Drop: {(df['rougeL_clean'] - df['rougeL_noise']).mean():.4f}
Avg BERT-F1 Drop: {(df['bert_F_clean'] - df['bert_F_noise']).mean():.4f}
Avg QA Acc Drop:  {(df['qa_accuracy_clean'] - df['qa_accuracy_noisy']).mean():.4f}

DATA STATISTICS:
---------------
Avg Article Length: {df['article_length'].mean():.0f} chars
Avg Abstract Length: {df['abstract_length'].mean():.0f} chars
Avg QA Pairs per Sample: {df['num_qa_pairs'].mean():.1f}
Avg Core Sentences: {df['num_core_sents'].mean():.1f}
Avg Noise Sentences: {df['num_noise_sents'].mean():.1f}
"""
        
        print(report)
        
        # Save report to file
        report_path = os.path.join(self.output_dir, 'experiment_report.txt')
        with open(report_path, 'w') as f:
            f.write(report)
        
        print(f"Report saved to: {report_path}")

if __name__ == "__main__":
    # Run the pilot experiment
    experiment = PilotExperiment(num_samples=100)
    results = experiment.run_experiment()
