#!/usr/bin/env python3
"""
Test script to verify the pilot experiment setup
Runs a mini experiment with just 2 samples to check everything works
"""

import os
import sys
from pilot_experiment import PilotExperiment

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    try:
        from datasets import load_dataset
        from transformers import AutoModelForCausalLM, AutoTokenizer
        import torch
        import pandas as pd
        from PIL import Image
        import imgkit
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_dataset_loading():
    """Test that the dataset can be loaded"""
    print("Testing dataset loading...")
    try:
        from datasets import load_dataset
        dataset = load_dataset("ccdv/arxiv-summarization", split="train")
        sample = dataset[0]
        print(f"✅ Dataset loaded successfully")
        print(f"   Sample keys: {list(sample.keys())}")
        print(f"   Article length: {len(sample['article'])} chars")
        print(f"   Abstract length: {len(sample['abstract'])} chars")
        return True
    except Exception as e:
        print(f"❌ Dataset loading error: {e}")
        return False

def test_model_initialization():
    """Test that models can be initialized"""
    print("Testing model initialization...")
    try:
        from core_selection import CoreSentenceSelector
        from qa_generation import QAGenerator
        
        print("  - Testing CoreSentenceSelector...")
        core_selector = CoreSentenceSelector()
        print("  ✅ CoreSentenceSelector initialized")
        
        print("  - Testing QAGenerator...")
        qa_generator = QAGenerator()
        print("  ✅ QAGenerator initialized")
        
        print("✅ All models initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Model initialization error: {e}")
        return False

def test_image_generation():
    """Test that image generation works"""
    print("Testing image generation...")
    try:
        from image_generation import ImageGenerator
        
        img_gen = ImageGenerator(img_dir="test_images")
        test_html = "<p>This is a test document.</p>"
        test_path = "test_images/test.png"
        
        os.makedirs("test_images", exist_ok=True)
        img_gen.html_to_png(test_html, test_path)
        
        if os.path.exists(test_path):
            print("✅ Image generation successful")
            # Clean up
            os.remove(test_path)
            os.rmdir("test_images")
            return True
        else:
            print("❌ Image file not created")
            return False
    except Exception as e:
        print(f"❌ Image generation error: {e}")
        return False

def run_mini_experiment():
    """Run a mini experiment with 2 samples"""
    print("Running mini experiment (2 samples)...")
    try:
        experiment = PilotExperiment(num_samples=2, output_dir="test_pilot_results")
        experiment.num_qa_pairs = 2  # Reduce QA pairs for faster testing
        
        results = experiment.run_experiment()
        
        if len(results) > 0:
            print(f"✅ Mini experiment completed successfully!")
            print(f"   Processed {len(results)} samples")
            
            # Clean up test results
            import shutil
            if os.path.exists("test_pilot_results"):
                shutil.rmtree("test_pilot_results")
            
            return True
        else:
            print("❌ No results generated")
            return False
            
    except Exception as e:
        print(f"❌ Mini experiment failed: {e}")
        return False

def main():
    """Run all tests"""
    print("="*60)
    print("PILOT EXPERIMENT TEST SUITE")
    print("="*60)
    
    tests = [
        ("Import Test", test_imports),
        ("Dataset Loading Test", test_dataset_loading),
        ("Model Initialization Test", test_model_initialization),
        ("Image Generation Test", test_image_generation),
        ("Mini Experiment Test", run_mini_experiment)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
    
    print("\n" + "="*60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Ready to run the full pilot experiment.")
        print("\nTo run the full experiment:")
        print("  python run_pilot.py")
    else:
        print("⚠️  Some tests failed. Please fix the issues before running the full experiment.")
        print("\nCommon fixes:")
        print("  - Install missing dependencies: pip install -r requirements_pilot.txt")
        print("  - Install wkhtmltopdf: sudo apt-get install wkhtmltopdf")
        print("  - Ensure sufficient GPU memory is available")
    
    print("="*60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
