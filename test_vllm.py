#!/usr/bin/env python3
"""
Simple test script to verify vLL<PERSON> is working with Qwen2.5-VL
"""

from vllm import LLM, SamplingParams
from PIL import Image
import numpy as np

def test_vllm_basic():
    """Test basic vLLM functionality with a simple image"""
    print("Testing vLLM with Qwen2.5-VL...")

    # Initialize vLLM - try with smaller model first
    llm = LLM(
        model="Qwen/Qwen2.5-VL-3B-Instruct",  # Use 3B model like in docs
        trust_remote_code=True,
        max_model_len=4096,
        limit_mm_per_prompt={"image": 1}
    )

    # Create sampling parameters
    sampling_params = SamplingParams(
        temperature=0.1,
        top_p=0.9,
        max_tokens=128
    )

    # Create a simple test image
    # Create a simple 256x256 RGB image with a gradient
    width, height = 256, 256
    array = np.zeros((height, width, 3), dtype=np.uint8)

    # Create a simple gradient pattern
    for i in range(height):
        for j in range(width):
            array[i, j] = [i % 256, j % 256, (i + j) % 256]

    image = Image.fromarray(array, 'RGB')

    # Try using chat interface instead
    messages = [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": "Describe this image."},
                {"type": "image_pil", "image_pil": image}
            ]
        }
    ]

    # Test the chat method
    print("Running inference...")
    outputs = llm.chat(messages, sampling_params=sampling_params)

    # Print results
    for output in outputs:
        print("Generated text:", output.outputs[0].text)

    print("vLLM test completed successfully!")

if __name__ == "__main__":
    test_vllm_basic()
