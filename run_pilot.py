#!/usr/bin/env python3
"""
Simple script to run the pilot experiment
"""

from pilot_experiment import PilotExperiment
import argparse

def main():
    parser = argparse.ArgumentParser(description='Run QA + Summarization Pilot Experiment')
    parser.add_argument('--samples', type=int, default=100, 
                       help='Number of samples to process (default: 100)')
    parser.add_argument('--output-dir', type=str, default='pilot_results',
                       help='Output directory for results (default: pilot_results)')
    parser.add_argument('--min-noise', type=int, default=3,
                       help='Minimum number of noise sentences (default: 3)')
    parser.add_argument('--max-noise', type=int, default=8,
                       help='Maximum number of noise sentences (default: 8)')
    parser.add_argument('--qa-pairs', type=int, default=5,
                       help='Number of QA pairs to generate per article (default: 5)')
    
    args = parser.parse_args()
    
    print("Starting Pilot Experiment with parameters:")
    print(f"  - Samples: {args.samples}")
    print(f"  - Output Directory: {args.output_dir}")
    print(f"  - Noise Range: {args.min_noise}-{args.max_noise} sentences")
    print(f"  - QA Pairs per Article: {args.qa_pairs}")
    print()
    
    # Create and run experiment
    experiment = PilotExperiment(
        num_samples=args.samples,
        output_dir=args.output_dir
    )
    
    # Update parameters
    experiment.min_noise = args.min_noise
    experiment.max_noise = args.max_noise
    experiment.num_qa_pairs = args.qa_pairs
    
    # Run the experiment
    try:
        results = experiment.run_experiment()
        print(f"\n✅ Experiment completed successfully!")
        print(f"📊 Processed {len(results)} samples")
        print(f"📁 Results saved to: {args.output_dir}")
        
    except KeyboardInterrupt:
        print("\n❌ Experiment interrupted by user")
    except Exception as e:
        print(f"\n❌ Experiment failed with error: {e}")
        raise

if __name__ == "__main__":
    main()
