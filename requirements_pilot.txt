# Requirements for the pilot experiment
# Core ML libraries
torch>=2.0.0
transformers>=4.35.0
datasets>=2.14.0
accelerate>=0.20.0

# Computer Vision and Image Processing
Pillow>=9.0.0
imgkit>=1.2.0

# NLP Evaluation
rouge-score>=0.1.2
bert-score>=0.3.13

# Data Processing
pandas>=1.5.0
numpy>=1.21.0
tqdm>=4.64.0

# Utilities
python-dateutil>=2.8.0

# Optional: For better performance
# torch-audio  # if needed for audio processing
# torchvision  # if needed for additional vision tasks
