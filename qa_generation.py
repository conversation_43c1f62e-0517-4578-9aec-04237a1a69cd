# qa_generation.py
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

class QAGenerator:
    def __init__(self, model_name="Qwen/Qwen3-8B"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_name, torch_dtype=torch.float16, device_map="auto"
        )

    def generate_qa(self, article, num_qas=3):
        prompt = (f"Generate {num_qas} single-hop diverse factual question-answer pairs based on the following passage. "
                  f"Return as a numbered list with each question and answer separated by '||'.\n\n{article}\n")
        messages = [{"role": "user", "content": prompt}]
        text_in = self.tokenizer.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        model_inputs = self.tokenizer([text_in], return_tensors="pt").to(self.model.device)
        with torch.no_grad():
            gen_ids = self.model.generate(**model_inputs, max_new_tokens=512)
        output = self.tokenizer.decode(gen_ids[0][model_inputs.input_ids.shape[1]:], skip_special_tokens=True)
        # Parse solution: expect format "1. Q || A\n2. Q || A\n..."
        qa_pairs = []
        for line in output.split('\n'):
            if '||' in line:
                q, a = line.split('||', 1)
                qa_pairs.append({'question': q.strip(), 'answer': a.strip()})
        return qa_pairs
