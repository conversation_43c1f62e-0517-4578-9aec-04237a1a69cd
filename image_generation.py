# image_generation.py
import random
import imgkit
import os

class ImageGenerator:
    def __init__(self, img_dir='images'):
        self.img_dir = img_dir
        if not os.path.exists(img_dir): os.makedirs(img_dir)

    def pick_noise_sentences(self, all_sents, core_sents, min_noise, max_noise):
        candidate_sents = [s for s in all_sents if s not in core_sents]
        n_noise = min(len(candidate_sents), random.randint(min_noise, max_noise))
        return random.sample(candidate_sents, n_noise) if candidate_sents else []

    def inject_visual_noise(self, text, target_sents):
        noise_record = []  # Track all injects with details!

        def random_style(sent):
            style = random.choice(['font-weight:bold;', 'font-style:italic;'])
            color = random.choice(['color:red;', 'color:blue;', 'color:green;'])
            size = random.choice(['font-size:16px;', 'font-size:18px;'])
            # For logging:
            style_type = 'bold' if 'bold' in style else 'italic'
            color_type = color.split(':')[1][:-1]  # 'red', 'blue', etc.
            size_type = size.replace('font-size:', '').replace('px;', '')
            noise_record.append({
                'sentence': sent,
                'style': style_type,
                'color': color_type,
                'size': size_type
            })
            return f'<span style="{style}{color}{size}">{sent}</span>'

        # Sort sentences by length (longest first) to avoid substring replacement issues
        sorted_target_sents = sorted(target_sents, key=len, reverse=True)

        modified = text
        successfully_replaced = []

        for sent in sorted_target_sents:
            # Clean the sentence (remove extra whitespace)
            clean_sent = sent.strip()
            if not clean_sent:
                continue

            # Check if this sentence exists in the current modified text
            if clean_sent in modified:
                styled_sent = random_style(clean_sent)
                # Replace only the first occurrence to avoid multiple replacements
                modified = modified.replace(clean_sent, styled_sent, 1)
                successfully_replaced.append(clean_sent)
            else:
                # Try to find the sentence with different punctuation/spacing
                import re
                # Escape special regex characters in the sentence
                escaped_sent = re.escape(clean_sent)
                # Look for the sentence with flexible whitespace and punctuation
                pattern = escaped_sent.replace(r'\ ', r'\s+').replace(r'\.', r'\.?')

                match = re.search(pattern, modified, re.IGNORECASE)
                if match:
                    original_match = match.group(0)
                    styled_sent = random_style(original_match)
                    modified = modified.replace(original_match, styled_sent, 1)
                    successfully_replaced.append(original_match)
                else:
                    print(f"Warning: Could not find sentence to style: '{clean_sent[:50]}...'")

        print(f"Successfully applied noise to {len(successfully_replaced)}/{len(target_sents)} sentences")
        return modified, noise_record

    def html_to_png(self, html_text, output_path):
        html_full = f'''
        <html>
        <head>
          <meta charset="utf-8">
          <style>
            body {{ font-family: Arial, sans-serif; font-size: 16px; width: 900px; padding: 20px; word-break: break-all; }}
            span {{ margin-right:2px; }}
          </style>
        </head>
        <body>{html_text}</body>
        </html>
        '''
        imgkit.from_string(html_full, output_path, options={'format': 'png', 'width':'900'})
