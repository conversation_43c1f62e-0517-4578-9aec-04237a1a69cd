# inference.py
from vllm import LLM, SamplingParams

class VLMInference:
    def __init__(self, model_name="Qwen/Qwen2.5-VL-7B-Instruct"):
        # Initialize vLLM with vision language model
        self.model = LLM(
            model=model_name,
            trust_remote_code=True,
            max_model_len=4096,
            limit_mm_per_prompt={"image": 1}
        )
        self.sampling_params = SamplingParams(
            temperature=0.1,
            top_p=0.9,
            max_tokens=128
        )

    def summarize_from_image(self, image_path, prompt="Generate a concise abstract based on the following introduction of the paper."):
        # Create message format for vLLM
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": [
                {"type": "image_url", "image_url": {"url": f"file://{image_path}"}},
                {"type": "text", "text": prompt}
            ]}
        ]

        # Generate response using vLLM
        outputs = self.model.chat(messages, sampling_params=self.sampling_params)
        return outputs[0].outputs[0].text

    def answer_question_from_image(self, image_path, question):
        """Answer a question based on an image"""
        # Create shorter sampling params for QA
        qa_sampling_params = SamplingParams(
            temperature=0.1,
            top_p=0.9,
            max_tokens=64
        )

        messages = [
            {"role": "system", "content": "You are a helpful assistant that answers questions about scientific documents."},
            {"role": "user", "content": [
                {"type": "image_url", "image_url": {"url": f"file://{image_path}"}},
                {"type": "text", "text": f"Question: {question}\n\nPlease provide a concise and accurate answer based on the content shown in the image."}
            ]}
        ]

        # Generate response using vLLM
        outputs = self.model.chat(messages, sampling_params=qa_sampling_params)
        return outputs[0].outputs[0].text

    def answer_qa_from_image(self, image_path, question):
        """Alternative QA method - delegates to answer_question_from_image"""
        return self.answer_question_from_image(image_path, question)
