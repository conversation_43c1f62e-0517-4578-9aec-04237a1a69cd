# inference.py
from PIL import Image
from transformers import AutoProcessor
import torch

class VLMInference:
    def __init__(self, model_name="Qwen/Qwen2.5-VL-7B-Instruct"):
        # Import the specific model class dynamically to avoid import issues
        from transformers import Qwen2_5_VLForConditionalGeneration
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_name, torch_dtype=torch.float16, device_map="auto", trust_remote_code=True
        )
        self.processor = AutoProcessor.from_pretrained(model_name, trust_remote_code=True)

    def summarize_from_image(self, image_path, prompt="Generate a concise abstract based on the following introduction of the paper."):
        pil_image = Image.open(image_path).convert("RGB")
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": [
                {"type": "image", "image": pil_image},
                {"type": "text", "text": prompt}
            ]}
        ]
        text_in = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
        inputs = self.processor(text=[text_in], images=[pil_image], padding=True, return_tensors="pt")
        inputs = inputs.to(self.model.device)
        generated_ids = self.model.generate(**inputs, max_new_tokens=128)
        generated_ids_trim = [out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)]
        output_text = self.processor.batch_decode(generated_ids_trim, skip_special_tokens=True, clean_up_tokenization_spaces=False)
        return output_text[0]

    def answer_question_from_image(self, image_path, question):
        """Answer a question based on an image"""
        pil_image = Image.open(image_path).convert("RGB")
        messages = [
            {"role": "system", "content": "You are a helpful assistant that answers questions about scientific documents."},
            {"role": "user", "content": [
                {"type": "image", "image": pil_image},
                {"type": "text", "text": f"Question: {question}\n\nPlease provide a concise and accurate answer based on the content shown in the image."}
            ]}
        ]
        text_in = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
        inputs = self.processor(text=[text_in], images=[pil_image], padding=True, return_tensors="pt")
        inputs = inputs.to(self.model.device)
        generated_ids = self.model.generate(**inputs, max_new_tokens=64)
        generated_ids_trim = [out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)]
        output_text = self.processor.batch_decode(generated_ids_trim, skip_special_tokens=True, clean_up_tokenization_spaces=False)
        return output_text[0]

    def answer_qa_from_image(self, image_path, question):
        """Alternative QA method - delegates to answer_question_from_image"""
        return self.answer_question_from_image(image_path, question)
