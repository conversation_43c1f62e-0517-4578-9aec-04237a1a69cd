# inference.py
from vllm import LLM, SamplingParams
from PIL import Image

class VLMInference:
    def __init__(self, model_name="Qwen/Qwen2.5-VL-7B-Instruct"):
        # Initialize vLLM with vision language model
        self.model = LLM(
            model=model_name,
            trust_remote_code=True,
            max_model_len=4096,
            limit_mm_per_prompt={"image": 1}
        )
        self.sampling_params = SamplingParams(
            temperature=0.1,
            top_p=0.9,
            max_tokens=128
        )

    def summarize_from_image(self, image_path, prompt="Generate a concise abstract based on the following introduction of the paper."):
        # Load image using PIL
        image = Image.open(image_path).convert("RGB")

        # Create the prompt in the format expected by Qwen2.5-VL
        # For Qwen2.5-VL, we need to use the USER/ASSISTANT format with <image> token
        full_prompt = f"USER: <image>\n{prompt}\nASSISTANT:"

        # Generate response using vLLM
        outputs = self.model.generate({
            "prompt": full_prompt,
            "multi_modal_data": {"image": image}
        }, sampling_params=self.sampling_params)

        return outputs[0].outputs[0].text

    def answer_question_from_image(self, image_path, question):
        """Answer a question based on an image"""
        # Create shorter sampling params for QA
        qa_sampling_params = SamplingParams(
            temperature=0.1,
            top_p=0.9,
            max_tokens=64
        )

        # Load image using PIL
        image = Image.open(image_path).convert("RGB")

        # Create the prompt in the format expected by Qwen2.5-VL
        full_prompt = f"USER: <image>\nQuestion: {question}\n\nPlease provide a concise and accurate answer based on the content shown in the image.\nASSISTANT:"

        # Generate response using vLLM
        outputs = self.model.generate({
            "prompt": full_prompt,
            "multi_modal_data": {"image": image}
        }, sampling_params=qa_sampling_params)

        return outputs[0].outputs[0].text

    def answer_qa_from_image(self, image_path, question):
        """Alternative QA method - delegates to answer_question_from_image"""
        return self.answer_question_from_image(image_path, question)
