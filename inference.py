# inference.py
# NOTE: This implementation uses vLLM for high-performance inference
from PIL import Image
from vllm import LLM, SamplingParams

class VLMInference:
    def __init__(self, model_name="Qwen/Qwen2.5-VL-7B-Instruct"):
        # Initialize vLLM with vision language model
        # Increase max_model_len to handle longer documents
        self.model = LLM(
            model=model_name,
            trust_remote_code=True,
            max_model_len=16384,  # Increased from 4096 to handle longer texts
            limit_mm_per_prompt={"image": 1}
        )
        self.sampling_params = SamplingParams(
            temperature=0.1,
            top_p=0.9,
            max_tokens=128
        )

    def summarize_from_image(self, image_path, prompt="Generate a concise abstract based on the following introduction of the paper."):
        # Load image using PIL
        image = Image.open(image_path).convert("RGB")

        # Use chat interface which works correctly with vLLM
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_pil", "image_pil": image}
                ]
            }
        ]

        # Generate response using vLLM chat interface
        outputs = self.model.chat(messages, sampling_params=self.sampling_params)

        return outputs[0].outputs[0].text

    def answer_question_from_image(self, image_path, question):
        """Answer a question based on an image"""
        # Create shorter sampling params for QA
        qa_sampling_params = SamplingParams(
            temperature=0.1,
            top_p=0.9,
            max_tokens=64
        )

        # Load image using PIL
        image = Image.open(image_path).convert("RGB")

        # Use chat interface which works correctly with vLLM
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": f"Question: {question}\n\nPlease provide a concise and accurate answer based on the content shown in the image."},
                    {"type": "image_pil", "image_pil": image}
                ]
            }
        ]

        # Generate response using vLLM chat interface
        outputs = self.model.chat(messages, sampling_params=qa_sampling_params)

        return outputs[0].outputs[0].text

    def answer_qa_from_image(self, image_path, question):
        """Alternative QA method - delegates to answer_question_from_image"""
        return self.answer_question_from_image(image_path, question)
