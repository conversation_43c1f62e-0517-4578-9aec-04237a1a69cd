# core_selection.py
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import re

class CoreSentenceSelector:
    def __init__(self, model_name="Qwen/Qwen3-8B"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_name, torch_dtype=torch.float16, device_map="auto"
        )

    def split_into_sentences(self, text):
        """
        Improved sentence splitting that handles scientific text better
        """
        # First, protect common abbreviations and patterns
        text = re.sub(r'\b(Dr|Prof|Mr|Mrs|Ms|vs|etc|i\.e|e\.g|al|Fig|Eq|Ref)\.\s', r'\1<DOT> ', text)
        text = re.sub(r'\b([A-Z][a-z]{1,3})\.\s([A-Z])', r'\1<DOT> \2', text)  # Names like "<PERSON><PERSON>"
        text = re.sub(r'(\d+)\.\s*(\d+)', r'\1<DOT>\2', text)  # Decimal numbers
        text = re.sub(r'(\d+)\.\s*([a-z])', r'\1<DOT> \2', text)  # Numbered lists

        # Split on sentence boundaries
        sentences = re.split(r'[.!?]+\s+', text)

        # Restore the protected dots and clean up
        sentences = [re.sub(r'<DOT>', '.', s.strip()) for s in sentences if s.strip()]

        # Filter out very short sentences (likely fragments)
        sentences = [s for s in sentences if len(s.split()) >= 3]

        return sentences

    def extract_core_sentences(self, article):
        prompt = ("Extract the most essential sentences that summarize the key points "
                  "of the following scientific article. Only output the sentences directly, separated by [SEP].\n\n" + article)
        messages = [{"role": "user", "content": prompt}]
        text_in = self.tokenizer.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        model_inputs = self.tokenizer([text_in], return_tensors="pt").to(self.model.device)
        with torch.no_grad():
            gen_ids = self.model.generate(**model_inputs, max_new_tokens=512)
        output = self.tokenizer.decode(gen_ids[0][model_inputs.input_ids.shape[1]:], skip_special_tokens=True)
        core_sents = [s.strip() for s in output.split("[SEP]") if s.strip()]

        # Use improved sentence splitting
        all_sents = self.split_into_sentences(article)

        return all_sents, core_sents
