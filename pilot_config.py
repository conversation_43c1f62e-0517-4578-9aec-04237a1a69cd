# pilot_config.py
"""
Configuration file for the pilot experiment
Modify these settings to customize your experiment
"""

# Dataset Configuration
DATASET_NAME = "ccdv/arxiv-summarization"
DATASET_SPLIT = "train"
NUM_SAMPLES = 100
RANDOM_SEED = 42

# Model Configuration
CORE_SELECTOR_MODEL = "Qwen/Qwen3-8B"
VLM_MODEL = "Qwen/Qwen2.5-VL-7B-Instruct"
QA_GENERATOR_MODEL = "Qwen/Qwen3-8B"

# Experiment Parameters
MIN_NOISE_SENTENCES = 16
MAX_NOISE_SENTENCES = 25
NUM_QA_PAIRS_PER_ARTICLE = 5

# Image Generation Settings
IMAGE_WIDTH = 900
IMAGE_PADDING = 20
FONT_FAMILY = "Arial, sans-serif"
BASE_FONT_SIZE = 16

# Noise Injection Settings
NOISE_STYLES = ['font-weight:bold;', 'font-style:italic;']
NOISE_COLORS = ['color:red;', 'color:blue;', 'color:green;']
NOISE_SIZES = ['font-size:16px;', 'font-size:18px;']

# Output Settings
OUTPUT_DIR = "pilot_results"
SAVE_IMAGES = True
SAVE_DETAILED_JSON = True
GENERATE_REPORT = True

# Evaluation Settings
ROUGE_METRICS = ['rougeL']
BERTSCORE_LANG = "en"

# Processing Settings
MAX_NEW_TOKENS_SUMMARY = 4096
MAX_NEW_TOKENS_QA = 64
MAX_NEW_TOKENS_CORE_EXTRACTION = 512

# Error Handling
CONTINUE_ON_ERROR = True
MAX_RETRIES = 3
TIMEOUT_SECONDS = 300

# Logging
LOG_LEVEL = "INFO"
LOG_TO_FILE = True
LOG_PROGRESS = True
