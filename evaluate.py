# evaluate.py
from rouge_score import rouge_scorer
from bert_score import score as bertscore

def evaluate_summaries(summary_clean, summary_noise, abstract_gold):
    scorer = rouge_scorer.RougeScorer(['rougeL'], use_stemmer=True)
    rouge_clean = scorer.score(abstract_gold, summary_clean)['rougeL'].fmeasure
    rouge_noise = scorer.score(abstract_gold, summary_noise)['rougeL'].fmeasure
    P_clean, R_clean, F_clean = bertscore([summary_clean], [abstract_gold], lang="en")
    P_noise, R_noise, F_noise = bertscore([summary_noise], [abstract_gold], lang="en")
    return {
        "rougeL_clean": float(rouge_clean),
        "rougeL_noise": float(rouge_noise),
        "bert_F_clean": float(F_clean[0]),
        "bert_F_noise": float(F_noise[0])
    }

def noise_impact_analysis(summary_noise, noise_sents):
    # Return sentences from noise_sents that are present in summary_noise
    used = [sent for sent in noise_sents if sent in summary_noise]
    return used

def evaluate_qa_accuracy(predicted_answers, gold_answers):
    correct = 0
    total = len(predicted_answers)
    for pred, gold in zip(predicted_answers, gold_answers):
        # Use a simple string equality; for more robust matching, use fuzzy matching
        if pred.strip().lower() == gold.strip().lower():
            correct += 1
    if total == 0:
        return 0.0
    return correct / total
